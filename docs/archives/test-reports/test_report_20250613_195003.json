{"timestamp": "2025-06-13T19:50:03.852458", "summary": {"passed": 14, "failed": 4, "warnings": 0, "errors": 0, "total_execution_time": 20.189376831054688}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.06687617301940918, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.02875065803527832, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0014140605926513672, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.03128194808959961, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5845129489898682, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.00859522819519043, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.12102389335632324, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.022764921188354492, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0030210018157958984, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.13290810585021973, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 5.678261995315552, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.018689870834350586, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.4526820182800293, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.7483088970184326, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.605512857437134, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.443075180053711, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.052758216857910156, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_195003.png"}]}
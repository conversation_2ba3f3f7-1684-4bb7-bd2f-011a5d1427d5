{"timestamp": "2025-06-13T19:39:04.921960", "summary": {"passed": 9, "failed": 9, "warnings": 0, "errors": 0, "total_execution_time": 862.9486989974976}, "results": [{"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "Missing <header> element; Missing <main> element; Page title missing or too short; Missing meta description; No H1 heading found", "execution_time": 0.03667616844177246, "recommendations": ["Add semantic <header> element", "Add semantic <main> element for primary content", "Add descriptive page title (50-60 characters)", "Add meta description for SEO", "Add exactly one H1 heading per page"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Insufficient ARIA landmarks", "execution_time": 0.012377738952636719, "recommendations": ["Add ARIA landmarks for better screen reader navigation"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0013260841369628906, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Limited navigation options", "execution_time": 0.019504070281982422, "recommendations": ["Provide adequate navigation options", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5360140800476074, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.004827737808227539, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.026159048080444336, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "FAILED", "severity": "LOW", "details": "Missing page title; Limited internal linking", "execution_time": 0.004761934280395508, "recommendations": ["Add descriptive page title", "Consider adding structured data for better SEO", "Add more internal links for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.003547191619873047, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.6743597984313965, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 244.52026891708374, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "FAILED", "severity": "HIGH", "details": "Tested 35 malicious inputs | Issues: 21", "execution_time": 603.6569490432739, "recommendations": ["Implement robust input validation", "Add length limits to inputs", "Sanitize user inputs", "Use content security policy"], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.2908051013946533, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 1.6792991161346436, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 6 boundary conditions | Issues: 0", "execution_time": 2.924161911010742, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.8370323181152344, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.16131901741027832, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 3 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.559309720993042, "recommendations": ["Test core user flows", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_193904.png"}]}
docs_dir: docs_build
site_name: FAAFO Career Platform Documentation
site_description: Comprehensive documentation for the FAAFO Career Platform
site_url: https://docs.faafo.com

# Repository
repo_name: dm601990/faafo
repo_url: https://github.com/dm601990/faafo
edit_uri: edit/main/docs/

# Theme
theme:
  name: material
  palette:
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.highlight
    - search.share
    - content.code.copy
    - content.action.edit

# Plugins
plugins:
  - search:
      lang: en
  - include-markdown:
      opening_tag: "{%"
      closing_tag: "%}"
      encoding: utf-8
  - tags

# Markdown Extensions
markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - admonition
  - pymdownx.details
  - pymdownx.tabbed:
      alternate_style: true
  - attr_list
  - md_in_html
  - tables
  - footnotes
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.keys
  - pymdownx.mark
  - pymdownx.tilde

# Navigation
nav:
  - Home: index.md
  - Quick Reference: quick-reference.md
  - Core:
    - Project Overview: core/project-overview.md
    - Architecture: core/architecture.md
    - Getting Started: core/getting-started.md
    - Conventions: core/conventions.md
  - Workflows:
    - Development: workflows/development.md
    - Testing: workflows/testing.md
    - Deployment: workflows/deployment.md
    - Troubleshooting: workflows/troubleshooting.md
  - Reference:
    - API: reference/api.md
    - Database: reference/database.md
    - Configuration: reference/configuration.md
    - Tags: reference/tags.md
  - Archives:
    - Implementation History: archives/implementation-history.md
    - Migration Logs: archives/migration-logs.md

# Extra
extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/dm601990/faafo
  analytics:
    provider: google
    property: G-XXXXXXXXXX  # Replace with actual GA4 property

# Copyright
copyright: Copyright &copy; 2025 FAAFO Career Platform

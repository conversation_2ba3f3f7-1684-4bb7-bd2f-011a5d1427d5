#!/usr/bin/env node

/**
 * Resume Builder Live Testing Script
 * Tests all implemented functionality without requiring user authentication
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';

// Test data
const LINKEDIN_TEST_DATA = `
<PERSON>
Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 5+ years developing web applications using React, Node.js, and Python. Passionate about creating scalable solutions and mentoring junior developers.

Experience:
Software Engineer - Tech Corp (2020 - Present)
- Developed and maintained React applications serving 100k+ users
- Led migration from legacy PHP system to modern Node.js architecture
- Mentored 3 junior developers and improved team productivity by 30%

Junior Developer - StartupXYZ (2018 - 2020)
- Built responsive web applications using HTML, CSS, JavaScript
- Collaborated with design team to implement pixel-perfect UIs
- Participated in agile development process and code reviews

Education:
Bachelor of Science in Computer Science
University of California, Berkeley (2014 - 2018)
GPA: 3.7

Skills:
JavaScript, React, Node.js, Python, SQL, Git, AWS, Docker
`;

const TEXT_RESUME_DATA = `
<PERSON>
Data Scientist
<EMAIL>
(*************
New York, NY

SUMMARY
Data scientist with 4+ years of experience in machine learning, statistical analysis, and data visualization. Expertise in Python, R, and SQL with a track record of delivering actionable insights.

EXPERIENCE
Senior Data Scientist | DataCorp | 2021 - Present
- Developed machine learning models that improved customer retention by 25%
- Created automated reporting dashboards using Tableau and Python
- Led cross-functional projects with engineering and product teams

Data Analyst | Analytics Inc | 2019 - 2021
- Performed statistical analysis on large datasets using R and SQL
- Built predictive models for sales forecasting
- Presented findings to executive leadership

EDUCATION
Master of Science in Statistics | Stanford University | 2017 - 2019
Bachelor of Arts in Mathematics | UCLA | 2013 - 2017

SKILLS
Python, R, SQL, Tableau, Machine Learning, Statistics, Data Visualization
`;

async function testAPI(endpoint, method = 'GET', body = null, headers = {}) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (body) {
      if (body instanceof FormData) {
        delete options.headers['Content-Type']; // Let fetch set it for FormData
        options.body = body;
      } else {
        options.body = JSON.stringify(body);
      }
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

async function testResumeParsingAPI() {
  console.log('\n🧪 Testing Resume Parsing API...');
  
  // Test 1: Health check
  console.log('1. Testing API health check...');
  const healthCheck = await testAPI('/api/ai/resume-parsing');
  console.log(`   Status: ${healthCheck.status}`);
  console.log(`   Response:`, healthCheck.data);
  
  if (!healthCheck.ok) {
    console.log('❌ API health check failed');
    return false;
  }
  
  // Test 2: LinkedIn Import
  console.log('\n2. Testing LinkedIn Import...');
  const linkedinTest = await testAPI('/api/ai/resume-parsing', 'POST', {
    resumeText: LINKEDIN_TEST_DATA,
    action: 'parse_linkedin'
  });
  
  console.log(`   Status: ${linkedinTest.status}`);
  if (linkedinTest.ok) {
    console.log('✅ LinkedIn parsing successful');
    console.log('   Parsed data preview:', {
      personalInfo: linkedinTest.data?.data?.parsedData?.personalInfo,
      experienceCount: linkedinTest.data?.data?.parsedData?.experience?.length || 0,
      skillsCount: linkedinTest.data?.data?.parsedData?.skills?.length || 0
    });
  } else {
    console.log('❌ LinkedIn parsing failed:', linkedinTest.data);
  }
  
  // Test 3: Text File Upload (simulated)
  console.log('\n3. Testing Text Resume Upload...');
  
  // Create a temporary text file
  const tempFile = path.join(__dirname, 'temp-resume.txt');
  fs.writeFileSync(tempFile, TEXT_RESUME_DATA);
  
  try {
    // Create FormData for file upload
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(tempFile), {
      filename: 'resume.txt',
      contentType: 'text/plain'
    });
    
    const uploadTest = await fetch(`${BASE_URL}/api/ai/resume-parsing`, {
      method: 'POST',
      body: formData
    });
    
    const uploadResult = await uploadTest.json();
    
    console.log(`   Status: ${uploadTest.status}`);
    if (uploadTest.ok) {
      console.log('✅ Text file upload successful');
      console.log('   Parsed data preview:', {
        personalInfo: uploadResult.data?.parsedData?.personalInfo,
        experienceCount: uploadResult.data?.parsedData?.experience?.length || 0,
        skillsCount: uploadResult.data?.parsedData?.skills?.length || 0
      });
    } else {
      console.log('❌ Text file upload failed:', uploadResult);
    }
  } catch (error) {
    console.log('❌ Text file upload error:', error.message);
  } finally {
    // Clean up temp file
    if (fs.existsSync(tempFile)) {
      fs.unlinkSync(tempFile);
    }
  }
  
  return true;
}

async function testAIAnalysisAPI() {
  console.log('\n🧪 Testing AI Resume Analysis API...');
  
  const testResumeText = `
John Doe - Software Engineer
Email: <EMAIL>
Phone: (*************

Summary: Experienced software engineer with 5 years of experience.

Experience:
Software Engineer at Tech Corp (2020-Present)
- Developed web applications
- Led team projects

Education:
BS Computer Science, UC Berkeley (2018)

Skills: JavaScript, React, Node.js
`;

  const analysisTest = await testAPI('/api/ai/resume-analysis', 'POST', {
    resumeText: testResumeText,
    format: 'text'
  });
  
  console.log(`   Status: ${analysisTest.status}`);
  if (analysisTest.ok) {
    console.log('✅ AI analysis successful');
    console.log('   Analysis preview:', {
      hasOverallScore: !!analysisTest.data?.data?.overallScore,
      hasStrengths: !!analysisTest.data?.data?.strengths,
      hasWeaknesses: !!analysisTest.data?.data?.weaknesses,
      hasSuggestions: !!analysisTest.data?.data?.suggestions
    });
  } else {
    console.log('❌ AI analysis failed:', analysisTest.data);
  }
  
  return analysisTest.ok;
}

async function testPageAccessibility() {
  console.log('\n🧪 Testing Page Accessibility...');
  
  // Test resume builder page
  console.log('1. Testing resume builder page...');
  const pageTest = await fetch(`${BASE_URL}/resume-builder`);
  console.log(`   Status: ${pageTest.status}`);
  
  if (pageTest.ok) {
    const html = await pageTest.text();
    const hasLinkedInImport = html.includes('Import from LinkedIn');
    const hasUploadExisting = html.includes('Upload Existing');
    const hasLoadingState = html.includes('Loading your resumes');
    
    console.log('✅ Resume builder page loads');
    console.log(`   Has LinkedIn Import: ${hasLinkedInImport ? '✅' : '❌'}`);
    console.log(`   Has Upload Existing: ${hasUploadExisting ? '✅' : '❌'}`);
    console.log(`   Has Loading State: ${hasLoadingState ? '✅' : '❌'}`);
    
    return hasLinkedInImport && hasUploadExisting;
  } else {
    console.log('❌ Resume builder page failed to load');
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Resume Builder Live Testing...');
  console.log(`📍 Testing against: ${BASE_URL}`);
  
  const results = {
    pageAccessibility: false,
    resumeParsingAPI: false,
    aiAnalysisAPI: false
  };
  
  try {
    results.pageAccessibility = await testPageAccessibility();
    results.resumeParsingAPI = await testResumeParsingAPI();
    results.aiAnalysisAPI = await testAIAnalysisAPI();
    
    console.log('\n📊 TEST RESULTS SUMMARY:');
    console.log('========================');
    console.log(`Page Accessibility: ${results.pageAccessibility ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Resume Parsing API: ${results.resumeParsingAPI ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`AI Analysis API: ${results.aiAnalysisAPI ? '✅ PASS' : '❌ FAIL'}`);
    
    const passCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
      console.log('🎉 ALL TESTS PASSED! Resume Builder functionality is working!');
    } else {
      console.log('⚠️  Some tests failed. Check the details above.');
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testResumeParsingAPI, testAIAnalysisAPI, testPageAccessibility };

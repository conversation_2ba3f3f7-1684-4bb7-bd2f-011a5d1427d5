# Database Optimization Procedures

## Overview

This document provides comprehensive database optimization procedures for the FAAFO Career Platform, including advanced connection pooling, query optimization, performance monitoring, and maintenance strategies.

## Prerequisites

- Database connection configured (see [Database Setup](../setup/database.md))
- Environment variables properly set
- Admin access for optimization features
- PostgreSQL with Neon/Vercel Postgres

## 🚀 Connection Pool Optimization

### Database Tier Configurations

#### Basic Tier (Development/Staging)
```env
DATABASE_TIER=basic
DB_MIN_CONNECTIONS=2
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=30000
DB_HEALTH_CHECK_INTERVAL=30000
DB_ENABLE_AUTO_SCALING=true
```

#### Standard Tier (Production)
```env
DATABASE_TIER=standard
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=15000
DB_IDLE_TIMEOUT=60000
DB_HEALTH_CHECK_INTERVAL=30000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
```

#### Premium Tier (High-Traffic)
```env
DATABASE_TIER=premium
DB_MIN_CONNECTIONS=10
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=20000
DB_IDLE_TIMEOUT=120000
DB_HEALTH_CHECK_INTERVAL=15000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
DB_ENABLE_LOAD_BALANCING=true
```

### Advanced Features

Enable production-grade features:

```env
# Connection Management
DB_CONNECTION_LEAK_DETECTION=true
DB_CONNECTION_LEAK_TIMEOUT=300000
DB_MAX_LIFETIME=3600000
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1000

# Performance Monitoring
DB_ENABLE_METRICS=true
DB_METRICS_RETENTION_HOURS=24
DB_SLOW_QUERY_THRESHOLD=1000
```

## 📊 Query Optimization

### Query Caching Configuration

```env
# Basic Query Caching
QUERY_CACHE_ENABLED=true
QUERY_CACHE_TTL=300000
QUERY_CACHE_SIZE=1000
RESULT_CACHE_ENABLED=true

# Redis Cache Integration
REDIS_CACHE_ENABLED=true
REDIS_URL=redis://localhost:6379
CACHE_DEFAULT_TTL=300
```

### Performance Indexes

Apply 163+ optimized indexes covering:

```bash
# Apply all performance indexes
curl -X POST /api/admin/database \
  -H "Content-Type: application/json" \
  -d '{"action": "apply_indexes"}'
```

**Index Categories:**
- **User Operations**: Email lookups, role-based queries, authentication
- **Learning Resources**: Category filtering, skill level searches, active content
- **Forum Operations**: Post retrieval, category browsing, user activity
- **Progress Tracking**: User progress, completion status, analytics
- **Full-text Search**: Content search across resources, posts, and paths

### Query Analysis and Optimization

Enable automatic query pattern analysis:

```env
# Query Analysis
ENABLE_QUERY_LOGGING=true
AUTO_OPTIMIZATION=false
METRICS_RETENTION=10000
```

Access query analysis via API:
```bash
# Get query patterns
curl "/api/admin/database/query-analysis?action=patterns"

# Detect N+1 queries
curl "/api/admin/database/query-analysis?action=n1_detection"

# Get optimization suggestions
curl "/api/admin/database/query-analysis?action=optimization_suggestions"
```

## 🔧 Performance Monitoring

### Real-time Monitoring Dashboard

Access comprehensive monitoring at `/admin/database`:

**Key Metrics:**
- Query execution times and patterns
- Connection pool utilization and health
- Cache hit rates and efficiency
- Slow query detection and alerts
- N+1 query pattern identification

### Health Checks

Configure automated health monitoring:

```env
DB_HEALTH_CHECK_INTERVAL=30000
DB_CONNECTION_LEAK_DETECTION=true
DB_SLOW_QUERY_THRESHOLD=1000
```

**Health Check Endpoints:**
- `GET /api/admin/database` - Comprehensive database status
- `GET /api/admin/database/connection-pool?action=health` - Connection pool health
- `GET /api/admin/database/query-analysis?action=cache_health` - Cache system health

## 🔄 Maintenance Procedures

### Daily Maintenance

```bash
# Check database health
curl "/api/admin/database"

# Monitor slow queries
curl "/api/admin/database/query-analysis?action=patterns"
```

### Weekly Maintenance

```bash
# Run database optimization (ANALYZE/VACUUM)
curl -X POST /api/admin/database \
  -H "Content-Type: application/json" \
  -d '{"action": "optimize"}'

# Clear old performance metrics
curl -X POST /api/admin/database \
  -d '{"action": "clear_metrics"}'

# Warm cache with popular data
curl -X POST /api/admin/database/query-analysis \
  -d '{"action": "warm_cache"}'
```

### Monthly Maintenance

```bash
# Generate comprehensive performance report
curl -X POST /api/admin/database \
  -d '{"action": "performance_report"}'

# Run auto-optimization
curl -X POST /api/admin/database \
  -d '{"action": "auto_optimize"}'
```

## 🚨 Troubleshooting

### High Connection Usage
**Symptoms:** Connection pool at 80%+ utilization
**Solutions:**
- Increase `DB_MAX_CONNECTIONS`
- Enable `DB_ENABLE_AUTO_SCALING`
- Check for connection leaks with `DB_CONNECTION_LEAK_DETECTION`
- Reduce `DB_IDLE_TIMEOUT` to free connections faster

### Slow Query Performance
**Symptoms:** Queries exceeding `SLOW_QUERY_THRESHOLD`
**Solutions:**
- Apply performance indexes: `{"action": "apply_indexes"}`
- Enable query caching: `QUERY_CACHE_ENABLED=true`
- Lower threshold for detection: `SLOW_QUERY_THRESHOLD=500`
- Review query patterns: `/api/admin/database/query-analysis?action=patterns`

### Cache Inefficiency
**Symptoms:** Cache hit rate below 50%
**Solutions:**
- Increase cache size: `QUERY_CACHE_SIZE=2000`
- Adjust TTL: `QUERY_CACHE_TTL=600000`
- Enable Redis caching: `REDIS_CACHE_ENABLED=true`
- Warm cache: `{"action": "warm_cache"}`

### Connection Failures
**Symptoms:** Connection success rate below 99%
**Solutions:**
- Increase timeout: `DB_CONNECTION_TIMEOUT=20000`
- Enable retry logic: `DB_RETRY_ATTEMPTS=3`
- Check network connectivity and SSL configuration
- Review database credentials and permissions

## 📈 Performance Targets

### Optimal Performance Metrics
- **Average Query Time**: < 250ms (target: < 500ms)
- **Cache Hit Rate**: > 75% (minimum: > 50%)
- **Connection Success Rate**: > 99%
- **Connection Pool Utilization**: 60-80% (avoid > 90%)
- **Slow Query Rate**: < 5% of total queries

### Load Testing

Run standard load tests:
```bash
curl -X POST /api/admin/database/connection-pool \
  -d '{"action": "run_standard_tests"}'
```

## 🔐 Security Considerations

### Production Security Settings
```env
# SSL/TLS Configuration
DATABASE_URL="postgresql://...?sslmode=require"

# Connection Security
DB_CONNECTION_TIMEOUT=10000
DB_MAX_LIFETIME=3600000
DB_CONNECTION_LEAK_DETECTION=true
```

### Access Control
- Admin-only access to optimization features
- Secure handling of query parameters and logs
- Connection limits to prevent exhaustion attacks
- Query monitoring to detect malicious patterns

## Related Procedures

- [Database Setup](../setup/database.md) - Initial database configuration
- [Security Testing](security-testing.md) - Database security validation
- [API Testing](api-testing.md) - Database API endpoint testing
- [Development Workflow](../../workflows/development.md) - Daily development procedures

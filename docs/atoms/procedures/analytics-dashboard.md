# Advanced Analytics Dashboard

## Overview

The Advanced Analytics Dashboard provides comprehensive insights into user engagement, learning progress, career path completion rates, and community participation for the FAAFO Career Platform.

## Features

### 📊 User Engagement Metrics
- **Total Users**: Complete user count across the platform
- **Active Users**: Daily, weekly, and monthly active user tracking
- **New User Registration**: Real-time new user metrics
- **User Retention**: Day 1, 7, and 30-day retention rates
- **Engagement Trends**: Historical user activity visualization

### 📚 Learning Progress Analytics
- **Resource Completion**: Total and completed learning resources
- **Completion Rates**: Overall platform completion statistics
- **Popular Resources**: Most completed resources with ratings
- **Learning Trends**: Daily completion and new starts tracking
- **Category Breakdown**: Performance by learning category

### 🎯 Career Path Metrics
- **Path Completion**: Enrollment and completion rates per career path
- **Path Popularity**: Bookmark and enrollment statistics
- **Progress Distribution**: User progress across different completion ranges
- **Time to Complete**: Average completion times (when available)

### 💬 Community Participation
- **Post Activity**: Total posts and replies tracking
- **Engagement Rate**: Reply-to-post ratio analysis
- **Top Contributors**: Most active community members
- **Category Activity**: Forum activity by category
- **Community Trends**: Daily community engagement patterns

## Technical Implementation

### Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Analytics Dashboard                      │
├─────────────────────────────────────────────────────────────┤
│  AdvancedAnalyticsDashboard.tsx (Main Component)           │
│  ├── MetricCard.tsx (Key Metrics Display)                  │
│  ├── LineChart.tsx (Trend Visualization)                   │
│  ├── BarChart.tsx (Category Comparisons)                   │
│  └── PieChart.tsx (Distribution Analysis)                  │
├─────────────────────────────────────────────────────────────┤
│  /api/analytics/dashboard (REST API)                       │
│  ├── GET: Fetch analytics data                             │
│  └── POST: Track custom events                             │
├─────────────────────────────────────────────────────────────┤
│  AnalyticsService (Business Logic)                         │
│  ├── getUserEngagementMetrics()                            │
│  ├── getLearningProgressMetrics()                          │
│  ├── getCareerPathMetrics()                                │
│  ├── getCommunityMetrics()                                 │
│  └── getComprehensiveAnalytics()                           │
├─────────────────────────────────────────────────────────────┤
│  Database Layer (Prisma ORM)                               │
│  ├── User engagement queries                               │
│  ├── Learning progress aggregations                        │
│  ├── Career path analytics                                 │
│  └── Community activity metrics                            │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

#### 1. AnalyticsService (`src/lib/analytics-service.ts`)
- Comprehensive data aggregation service
- Optimized database queries with date filtering
- Real-time trend calculation
- Security-validated input handling

#### 2. API Endpoints (`src/app/api/analytics/dashboard/`)
- **GET**: Retrieve analytics data with time range filtering
- **POST**: Track custom analytics events
- Authentication required for all endpoints
- Input sanitization and validation

#### 3. Chart Components (`src/components/analytics/charts/`)
- **LineChart**: Time-series trend visualization
- **BarChart**: Category and comparison charts
- **PieChart**: Distribution and percentage analysis
- Responsive design with Recharts library

#### 4. Dashboard Interface (`src/components/analytics/AdvancedAnalyticsDashboard.tsx`)
- Tabbed interface for different metric categories
- Time range selection (7, 30, 90, 365 days)
- Real-time data refresh capabilities
- Export functionality for analytics data

## API Usage

### Fetch Analytics Data

```typescript
// Get comprehensive analytics for last 30 days
GET /api/analytics/dashboard?range=30&metric=all

// Get specific metrics
GET /api/analytics/dashboard?range=7&metric=user-engagement
GET /api/analytics/dashboard?range=90&metric=learning-progress
GET /api/analytics/dashboard?range=365&metric=community
```

### Track Custom Events

```typescript
POST /api/analytics/dashboard
{
  "eventType": "page_view",
  "eventData": { "page": "/dashboard" },
  "metadata": { "userAgent": "..." }
}
```

## Data Models

### User Engagement Metrics
```typescript
interface UserEngagementMetrics {
  totalUsers: number;
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  newUsers: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  userRetention: {
    day1: number;
    day7: number;
    day30: number;
  };
  engagementTrends: Array<{
    date: string;
    activeUsers: number;
    newUsers: number;
  }>;
}
```

### Learning Progress Metrics
```typescript
interface LearningProgressMetrics {
  totalResources: number;
  completedResources: number;
  completionRate: number;
  popularResources: Array<{
    id: string;
    title: string;
    completions: number;
    averageRating: number;
  }>;
  categoryBreakdown: Array<{
    category: string;
    totalResources: number;
    completedResources: number;
    completionRate: number;
  }>;
}
```

## Security Features

- **Input Sanitization**: All user inputs are sanitized using SecurityValidator
- **Authentication Required**: All endpoints require valid user session
- **SQL Injection Prevention**: Parameterized queries with Prisma ORM
- **Rate Limiting**: API endpoints are rate-limited
- **Data Validation**: Comprehensive input validation with Zod schemas

## Performance Optimizations

- **Database Indexing**: Optimized queries with proper indexing
- **Caching**: Response caching for frequently accessed data
- **Pagination**: Large datasets are paginated
- **Lazy Loading**: Charts load progressively
- **Memoization**: React components use useMemo for expensive calculations

## Testing

### Test Coverage
- **Unit Tests**: 100% coverage for AnalyticsService
- **API Tests**: Complete endpoint testing with mocking
- **Component Tests**: React component testing with React Testing Library
- **Integration Tests**: End-to-end workflow testing

### Test Files
- `__tests__/analytics/analytics-service.test.ts`
- `__tests__/analytics/analytics-api.test.ts`
- `__tests__/analytics/MetricCard.test.tsx`
- `__tests__/analytics/AdvancedAnalyticsDashboard.test.tsx`

## Usage Instructions

### For Administrators
1. Navigate to `/analytics` in the application
2. Select desired time range from dropdown
3. Switch between tabs to view different metric categories
4. Use refresh button to update data in real-time
5. Export data using the export button

### For Developers
1. Import analytics service: `import { analyticsService } from '@/lib/analytics-service'`
2. Use API endpoints for custom integrations
3. Extend chart components for new visualizations
4. Add custom metrics to the analytics service

## Future Enhancements

- **Real-time Updates**: WebSocket integration for live data
- **Advanced Filtering**: Custom date ranges and filters
- **Predictive Analytics**: ML-based trend predictions
- **Custom Dashboards**: User-configurable dashboard layouts
- **Data Export**: CSV, PDF, and Excel export options
- **Alerting System**: Automated alerts for metric thresholds

## Dependencies

- **Recharts**: Chart visualization library
- **date-fns**: Date manipulation utilities
- **Prisma**: Database ORM
- **Next.js**: API routes and server-side rendering
- **React**: Component framework
- **TypeScript**: Type safety and development experience

# Documentation Style Guide
## AI-Optimized Documentation Standards

### Core Principles

#### 1. Atomic Content Architecture
- **One Concept, One File**: Each atom should describe exactly one self-contained procedure, concept, or configuration
- **Reusability Test**: If a step could be reused independently in 3+ other workflows, it should be a separate atom
- **Granularity Rule**: An atom should be completable in one sitting (5-15 minutes of reading/execution)

#### 2. Metadata Schema (REQUIRED)

Every documentation file MUST include this YAML frontmatter:

```yaml
---
# REQUIRED FIELDS
doc_type: "atomic-procedure" | "atomic-concept" | "atomic-config" | "workflow" | "reference"
title: "Clear, action-oriented title"
tags: ["database", "setup", "backend"]  # 2-5 relevant tags
owner: "@team-name"  # GitHub team or individual
last_validated: "2024-01-15"  # YYYY-MM-DD format

# CONDITIONAL FIELDS
dependencies: ["atoms/auth/setup.md"]  # For workflows only
difficulty: "beginner" | "intermediate" | "advanced"  # For procedures
estimated_time: "5 minutes"  # For procedures
prerequisites: ["Docker installed", "Database running"]  # For procedures

# AUTO-GENERATED (DO NOT EDIT)
used_in: []  # Populated by build system
---
```

#### 3. Content Structure Standards

**Atomic Procedures:**
```markdown
# Title (Action-oriented: "Configure Database Connection")

## Overview
Brief description of what this accomplishes and why.

## Prerequisites
- Specific requirements
- Links to dependency atoms

## Steps
1. Clear, numbered steps
2. Include code blocks with syntax highlighting
3. Explain expected outcomes

## Verification
How to confirm the procedure worked.

## Troubleshooting
Common issues and solutions.
```

**Atomic Concepts:**
```markdown
# Title (Noun-oriented: "Database Connection Pooling")

## Definition
Clear, concise explanation.

## Why It Matters
Context and importance.

## Key Components
- Bullet points of main elements
- Links to related procedures

## Examples
Concrete examples with code.

## Related Concepts
Links to other concept atoms.
```

### Enforcement Mechanisms

#### 1. CI/CD Validation Pipeline

```yaml
# .github/workflows/docs-validation.yml
name: Documentation Validation
on: [pull_request]
jobs:
  validate-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Validate Documentation
        run: |
          python scripts/validate-docs.py
          # Checks:
          # - Required frontmatter fields
          # - Valid doc_type values
          # - Tag consistency
          # - Link validation
          # - File naming conventions
```

#### 2. Pre-commit Hooks

```bash
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: docs-metadata-check
        name: Documentation Metadata Validation
        entry: python scripts/validate-metadata.py
        language: python
        files: '^docs/.*\.md$'
```

#### 3. Documentation Linter Rules

**File Naming Convention:**
- Atoms: `atoms/{category}/{specific-name}.md`
- Workflows: `workflows/{use-case}.md`
- References: `reference/{topic}.md`

**Required Checks:**
- ✅ Valid YAML frontmatter
- ✅ Required fields present
- ✅ Valid enum values (doc_type, difficulty)
- ✅ Date format validation
- ✅ Tag consistency across repository
- ✅ Link validation (internal and external)
- ✅ Proper heading hierarchy

### Team Responsibilities

#### Documentation Owners
- **Backend Team**: Database, API, infrastructure atoms
- **Frontend Team**: UI, component, styling atoms  
- **DevOps Team**: Deployment, monitoring, security atoms
- **Product Team**: Workflow composition and user journeys

#### Review Process
1. **Technical Review**: Owner team validates accuracy
2. **Style Review**: Documentation team validates metadata and structure
3. **AI Review**: Automated checks for metadata completeness
4. **User Review**: Test with actual use cases

### Quality Metrics

#### Metadata Quality Score
- Required fields complete: 40 points
- Optional fields complete: 20 points
- Valid tag usage: 20 points
- Recent validation date: 20 points
- **Target**: 90+ points per document

#### Discoverability Score
- Proper categorization: 25 points
- Rich tag usage: 25 points
- Clear dependencies: 25 points
- Usage tracking: 25 points
- **Target**: 85+ points per document

### Migration Standards

#### Phase 1: Foundation (Week 1)
- Set up validation pipeline
- Create initial atom templates
- Train team on metadata standards

#### Phase 2: Core Atomization (Weeks 2-4)
- Convert 20 most-used procedures to atoms
- Establish tag taxonomy
- Build dependency graph

#### Phase 3: Workflow Composition (Weeks 5-6)
- Create 5 key user workflows
- Test transclusion system
- Validate AI retrieval quality

#### Phase 4: Full Migration (Weeks 7-12)
- Convert remaining documentation
- Optimize search and discovery
- Establish maintenance processes

### Success Criteria

#### For AI Systems
- 95% successful retrieval of relevant atoms
- Context completeness score > 90%
- Zero broken internal links

#### For Human Users
- 80% of users find information in < 2 minutes
- 90% of procedures work on first attempt
- Documentation satisfaction score > 4.5/5

#### For Maintainers
- 50% reduction in merge conflicts
- 75% reduction in duplicate content
- 90% of updates require editing only 1 file

### Violation Handling

#### Automated Enforcement
- **PR Blocking**: Missing required metadata blocks merge
- **Warning System**: Outdated validation dates trigger notifications
- **Cleanup Jobs**: Weekly scans for orphaned files and broken links

#### Human Escalation
- **Style Violations**: Documentation team provides feedback
- **Content Issues**: Owner team responsible for corrections
- **Persistent Problems**: Engineering manager involvement

### Tools and Resources

#### Required Tools
- **Linter**: Custom Python script for metadata validation
- **Link Checker**: Automated internal/external link validation
- **Tag Manager**: Tool for consistent tag usage across repository
- **Dependency Tracker**: Automatic used_in field population

#### Recommended Tools
- **VS Code Extension**: Real-time metadata validation
- **Documentation Dashboard**: Health metrics and quality scores
- **AI Testing Suite**: Validate retrieval quality for different queries

This style guide is a living document. Updates require approval from the Documentation Architecture Committee (Backend Lead + Frontend Lead + DevOps Lead + Product Lead).
